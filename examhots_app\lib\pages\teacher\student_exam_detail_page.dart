import 'package:flutter/material.dart';
import '../../services/api_service.dart';
import '../../config/app_config.dart';

class StudentExamDetailPage extends StatefulWidget {
  final int examId;
  final int studentId;
  final String studentName;
  final String examName;

  const StudentExamDetailPage({
    super.key,
    required this.examId,
    required this.studentId,
    required this.studentName,
    required this.examName,
  });

  @override
  State<StudentExamDetailPage> createState() => _StudentExamDetailPageState();
}

class _StudentExamDetailPageState extends State<StudentExamDetailPage>
    with SingleTickerProviderStateMixin {
  Map<String, dynamic>? _examData;
  bool _isLoading = true;
  String? _errorMessage;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _loadExamDetail();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadExamDetail() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.getStudentExamDetail(
        widget.examId,
        widget.studentId,
      );

      if (response['success'] == true) {
        setState(() {
          _examData = response['data'];
          _isLoading = false;

          // Initialize tab controller based on available question types
          final questionsByType =
              _examData!['questions_by_type'] as Map<String, dynamic>;
          _tabController = TabController(
            length: questionsByType.keys.length,
            vsync: this,
          );
        });
      } else {
        throw Exception(response['message'] ?? 'Failed to load exam detail');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
      });
    }
  }

  Future<void> _updateEssayScore(
    int questionId,
    double currentScore,
    double maxScore,
  ) async {
    final TextEditingController scoreController = TextEditingController(
      text: currentScore.toString(),
    );

    final result = await showDialog<double>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Update Skor Esai'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Skor maksimal: $maxScore'),
                const SizedBox(height: 16),
                TextField(
                  controller: scoreController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Skor',
                    border: const OutlineInputBorder(),
                    suffixText: '/ $maxScore',
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Batal'),
              ),
              ElevatedButton(
                onPressed: () {
                  final score = double.tryParse(scoreController.text) ?? 0;
                  if (score >= 0 && score <= maxScore) {
                    Navigator.pop(context, score);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Skor harus antara 0 dan $maxScore'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF455A9D),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Simpan'),
              ),
            ],
          ),
    );

    if (result != null) {
      try {
        final response = await ApiService.updateStudentEssayScore(
          widget.examId,
          widget.studentId,
          questionId,
          result,
        );

        if (response['success'] == true && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Skor esai berhasil diperbarui'),
              backgroundColor: Colors.green,
            ),
          );
          _loadExamDetail(); // Reload to get updated data
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Gagal memperbarui skor: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  // Helper method to get question score info
  Map<String, dynamic> _getQuestionScoreInfo(Map<String, dynamic> question) {
    final questionType = question['type'] as String;
    final studentAnswer = question['student_answer'];

    if (questionType == 'pilihan_ganda') {
      final answers = question['answers'] as List<dynamic>;
      final selectedAnswerId = studentAnswer?['selected_answer'];
      final selectedAnswer =
          answers
              .where((answer) => answer['id'] == selectedAnswerId)
              .firstOrNull;
      final isCorrect =
          selectedAnswer != null && selectedAnswer['is_correct'] == 1;

      // For multiple choice, score is calculated based on total PG score / number of PG questions
      // This would need to be passed from the exam data, for now we'll show if correct/incorrect
      return {
        'isCorrect': isCorrect,
        'hasAnswer': selectedAnswerId != null,
        'scoreText':
            isCorrect
                ? 'Benar'
                : (selectedAnswerId != null ? 'Salah' : 'Tidak Dijawab'),
        'color':
            isCorrect
                ? Colors.green
                : (selectedAnswerId != null ? Colors.red : Colors.grey),
      };
    } else if (questionType == 'uraian_singkat') {
      final answers = question['answers'] as List<dynamic>;
      dynamic correctAnswer =
          answers.where((answer) => answer['is_correct'] == 1).firstOrNull;
      correctAnswer ??=
          answers.where((answer) => answer['is_correct'] == true).firstOrNull;
      correctAnswer ??= answers.isNotEmpty ? answers.first : null;

      final studentAnswerText = studentAnswer?['text_answer'] ?? '';
      bool isCorrect = false;
      if (correctAnswer != null && studentAnswerText.isNotEmpty) {
        final studentNormalized = studentAnswerText.toLowerCase().trim();
        final correctNormalized =
            (correctAnswer['answer'] ?? '').toString().toLowerCase().trim();
        isCorrect = studentNormalized == correctNormalized;
      }

      return {
        'isCorrect': isCorrect,
        'hasAnswer': studentAnswerText.isNotEmpty,
        'scoreText':
            isCorrect
                ? 'Benar'
                : (studentAnswerText.isNotEmpty ? 'Salah' : 'Tidak Dijawab'),
        'color':
            isCorrect
                ? Colors.green
                : (studentAnswerText.isNotEmpty ? Colors.red : Colors.grey),
      };
    } else if (questionType == 'esai') {
      final currentScore = studentAnswer?['essay_score']?.toDouble() ?? 0.0;
      final answers = question['answers'] as List<dynamic>;
      dynamic essayAnswer;

      if (answers.isNotEmpty) {
        essayAnswer = answers.firstWhere(
          (answer) => answer['is_correct'] == 1,
          orElse: () => answers.first,
        );
      }

      final maxScore =
          essayAnswer != null
              ? (essayAnswer['score'] != null
                  ? double.tryParse(essayAnswer['score'].toString()) ?? 100.0
                  : 100.0)
              : 100.0;

      return {
        'isCorrect': currentScore > 0,
        'hasAnswer': (studentAnswer?['text_answer'] ?? '').isNotEmpty,
        'scoreText': '${currentScore.toInt()}/${maxScore.toInt()}',
        'color': currentScore > 0 ? Colors.blue : Colors.grey,
      };
    }

    return {
      'isCorrect': false,
      'hasAnswer': false,
      'scoreText': 'Tidak Dijawab',
      'color': Colors.grey,
    };
  }

  Widget _buildQuestionCard(Map<String, dynamic> question, int index) {
    final questionType = question['type'] as String;
    final studentAnswer = question['student_answer'];
    final scoreInfo = _getQuestionScoreInfo(question);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Question header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF455A9D).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Soal ${index + 1}',
                    style: const TextStyle(
                      color: Color(0xFF455A9D),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                // Score badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: (scoreInfo['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    scoreInfo['scoreText'] as String,
                    style: TextStyle(
                      color: scoreInfo['color'] as Color,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _getQuestionTypeLabel(questionType),
                    style: TextStyle(
                      color: Colors.grey[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Question text
            Text(
              question['question'],
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF1F2937),
              ),
            ),

            // Question images if exist
            if (question['img'] != null && question['img'].isNotEmpty) ...[
              const SizedBox(height: 12),
              ..._buildQuestionImages(question['img']),
            ],

            const SizedBox(height: 16),

            // Answer section based on question type
            if (questionType == 'pilihan_ganda')
              _buildMultipleChoiceAnswer(question, studentAnswer)
            else if (questionType == 'uraian_singkat')
              _buildShortAnswerAnswer(question, studentAnswer)
            else if (questionType == 'esai')
              _buildEssayAnswer(question, studentAnswer),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleChoiceAnswer(
    Map<String, dynamic> question,
    dynamic studentAnswer,
  ) {
    final answers = question['answers'] as List<dynamic>;
    final selectedAnswerId = studentAnswer?['selected_answer'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Pilihan Jawaban:',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        ...answers.asMap().entries.map((entry) {
          final index = entry.key;
          final answer = entry.value;
          final isSelected = answer['id'] == selectedAnswerId;
          final isCorrect = answer['is_correct'] == 1;

          Color backgroundColor = Colors.grey[50]!;
          Color borderColor = Colors.grey[300]!;
          Color textColor = Colors.grey[700]!;

          if (isSelected && isCorrect) {
            backgroundColor = Colors.green[50]!;
            borderColor = Colors.green[300]!;
            textColor = Colors.green[700]!;
          } else if (isSelected && !isCorrect) {
            backgroundColor = Colors.red[50]!;
            borderColor = Colors.red[300]!;
            textColor = Colors.red[700]!;
          } else if (!isSelected && isCorrect) {
            backgroundColor = Colors.blue[50]!;
            borderColor = Colors.blue[300]!;
            textColor = Colors.blue[700]!;
          }

          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: backgroundColor,
              border: Border.all(color: borderColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Text(
                  '${String.fromCharCode(65 + index)}.',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    answer['answer'],
                    style: TextStyle(color: textColor),
                  ),
                ),
                if (isSelected)
                  Icon(
                    isCorrect ? Icons.check_circle : Icons.cancel,
                    color: isCorrect ? Colors.green : Colors.red,
                    size: 20,
                  )
                else if (isCorrect)
                  const Icon(
                    Icons.check_circle_outline,
                    color: Colors.blue,
                    size: 20,
                  ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }

  Widget _buildShortAnswerAnswer(
    Map<String, dynamic> question,
    dynamic studentAnswer,
  ) {
    // Try to find correct answer with different approaches
    final answers = question['answers'] as List<dynamic>;
    dynamic correctAnswer;

    // First try: find answer with is_correct == 1
    correctAnswer =
        answers.where((answer) => answer['is_correct'] == 1).firstOrNull;

    // Second try: find answer with is_correct == true
    if (correctAnswer == null) {
      correctAnswer =
          answers.where((answer) => answer['is_correct'] == true).firstOrNull;
    }

    // Third try: for uraian_singkat, there should be only one answer which is the correct one
    if (correctAnswer == null && answers.isNotEmpty) {
      correctAnswer = answers.first;
    }

    final studentAnswerText = studentAnswer?['text_answer'] ?? '';

    bool isCorrect = false;
    if (correctAnswer != null && studentAnswerText.isNotEmpty) {
      final studentNormalized = studentAnswerText.toLowerCase().trim();
      final correctNormalized =
          (correctAnswer['answer'] ?? '').toString().toLowerCase().trim();
      isCorrect = studentNormalized == correctNormalized;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Correct answer
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            border: Border.all(color: Colors.blue[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Jawaban yang Benar:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
              const SizedBox(height: 4),
              Text(
                correctAnswer != null
                    ? (correctAnswer['answer'] ?? 'Jawaban tidak tersedia')
                        .toString()
                    : 'Tidak ada jawaban yang benar',
                style: TextStyle(color: Colors.blue[700]),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // Student answer
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isCorrect ? Colors.green[50] : Colors.red[50],
            border: Border.all(
              color: isCorrect ? Colors.green[300]! : Colors.red[300]!,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Jawaban Siswa:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: isCorrect ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                  const Spacer(),
                  if (studentAnswerText.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: isCorrect ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        isCorrect ? 'Benar' : 'Salah',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                studentAnswerText.isEmpty ? 'Tidak dijawab' : studentAnswerText,
                style: TextStyle(
                  color: isCorrect ? Colors.green[700] : Colors.red[700],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEssayAnswer(
    Map<String, dynamic> question,
    dynamic studentAnswer,
  ) {
    // For essay questions, get the score from the answer (there should be only one answer for essay)
    final answers = question['answers'] as List<dynamic>;
    dynamic essayAnswer;

    // Find the correct answer (should be the only one for essay questions)
    if (answers.isNotEmpty) {
      essayAnswer = answers.firstWhere(
        (answer) => answer['is_correct'] == 1,
        orElse:
            () =>
                answers.first, // Fallback to first answer if no is_correct flag
      );
    }

    // Get max score from the answer's score field, with proper type conversion
    final maxScore =
        essayAnswer != null
            ? (essayAnswer['score'] != null
                ? double.tryParse(essayAnswer['score'].toString()) ?? 100.0
                : 100.0)
            : 100.0;

    final currentScore = studentAnswer?['essay_score']?.toDouble() ?? 0.0;
    final studentAnswerText = studentAnswer?['text_answer'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Student answer
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Jawaban Siswa:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF374151),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                studentAnswerText.isEmpty ? 'Tidak dijawab' : studentAnswerText,
                style: const TextStyle(color: Color(0xFF374151)),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // Scoring section
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFF455A9D).withValues(alpha: 0.1),
            border: Border.all(
              color: const Color(0xFF455A9D).withValues(alpha: 0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'Penilaian Guru:',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF455A9D),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Skor maksimal: $maxScore',
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: Text(
                      '$currentScore / $maxScore',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF455A9D),
                      ),
                    ),
                  ),
                  ElevatedButton(
                    onPressed:
                        () => _updateEssayScore(
                          question['id'],
                          currentScore,
                          maxScore,
                        ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF455A9D),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    child: const Text('Edit Nilai'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getQuestionTypeLabel(String type) {
    switch (type) {
      case 'pilihan_ganda':
        return 'Pilihan Ganda';
      case 'uraian_singkat':
        return 'Uraian Singkat';
      case 'esai':
        return 'Esai';
      default:
        return type;
    }
  }

  List<Widget> _buildQuestionImages(String imgString) {
    // Split by comma to handle multiple images
    final images =
        imgString.split(',').where((img) => img.trim().isNotEmpty).toList();

    return images.map((image) {
      final imageUrl = AppConfig.getQuestionImageUrl(image.trim());

      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Image.network(
            imageUrl,
            width: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.broken_image, color: Colors.grey, size: 32),
                      SizedBox(height: 4),
                      Text(
                        'Gambar tidak dapat dimuat',
                        style: TextStyle(color: Colors.grey, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              );
            },
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Container(
                height: 150,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: CircularProgressIndicator(color: Color(0xFF455A9D)),
                ),
              );
            },
          ),
        ),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: const Color(0xFFF8FAFC),
        appBar: AppBar(
          backgroundColor: const Color(0xFF455A9D),
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'Detail Jawaban Siswa',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(color: Color(0xFF455A9D)),
        ),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: const Color(0xFFF8FAFC),
        appBar: AppBar(
          backgroundColor: const Color(0xFF455A9D),
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.white),
            onPressed: () => Navigator.pop(context),
          ),
          title: const Text(
            'Detail Jawaban Siswa',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
              const SizedBox(height: 16),
              Text(
                _errorMessage!,
                style: TextStyle(color: Colors.grey[600], fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadExamDetail,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF455A9D),
                  foregroundColor: Colors.white,
                ),
                child: const Text('Coba Lagi'),
              ),
            ],
          ),
        ),
      );
    }

    final questionsByType =
        _examData!['questions_by_type'] as Map<String, dynamic>;
    final examResult = _examData!['exam_result'];

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        backgroundColor: const Color(0xFF455A9D),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Detail Jawaban Siswa',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              widget.studentName,
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
          ],
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(100),
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'Total Skor',
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                        Text(
                          examResult['score'].toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        const Text(
                          'Status',
                          style: TextStyle(color: Colors.white70, fontSize: 12),
                        ),
                        Text(
                          examResult['status'] ?? 'completed',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      body:
          questionsByType.isEmpty
              ? const Center(
                child: Text(
                  'Tidak ada soal ditemukan',
                  style: TextStyle(color: Colors.grey, fontSize: 16),
                ),
              )
              : DefaultTabController(
                length: questionsByType.keys.length,
                child: Column(
                  children: [
                    Container(
                      color: Colors.white,
                      child: TabBar(
                        labelColor: const Color(0xFF455A9D),
                        unselectedLabelColor: Colors.grey,
                        indicatorColor: const Color(0xFF455A9D),
                        tabs:
                            questionsByType.keys.map((type) {
                              return Tab(text: _getQuestionTypeLabel(type));
                            }).toList(),
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        children:
                            questionsByType.entries.map((entry) {
                              final questions = entry.value as List<dynamic>;
                              return ListView.builder(
                                padding: const EdgeInsets.all(16),
                                itemCount: questions.length,
                                itemBuilder: (context, index) {
                                  return _buildQuestionCard(
                                    questions[index],
                                    index,
                                  );
                                },
                              );
                            }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
    );
  }
}
