import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/exam_provider.dart';
import '../../models/exam.dart';

class ExamFormPage extends StatefulWidget {
  final Exam? exam;

  const ExamFormPage({super.key, this.exam});

  @override
  State<ExamFormPage> createState() => _ExamFormPageState();
}

class _ExamFormPageState extends State<ExamFormPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _startDateController = TextEditingController();
  final _endDateController = TextEditingController();
  final _startTimeController = TextEditingController();
  final _endTimeController = TextEditingController();
  final _durationController = TextEditingController();
  final _kkmController = TextEditingController();
  final _trialsController = TextEditingController();

  int? _selectedClassId;
  int? _selectedQuestionMaterialId;
  List<int> _selectedQuestionIds = [];
  bool _isLoading = false;

  bool get isEditing => widget.exam != null;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _loadFormData();
      if (isEditing) {
        _populateFields();
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _startDateController.dispose();
    _endDateController.dispose();
    _startTimeController.dispose();
    _endTimeController.dispose();
    _durationController.dispose();
    _kkmController.dispose();
    _trialsController.dispose();
    super.dispose();
  }

  Future<void> _loadFormData() async {
    await context.read<ExamProvider>().loadFormData();
  }

  void _populateFields() {
    final exam = widget.exam!;
    _nameController.text = exam.name;
    _startDateController.text = exam.startdate;
    _endDateController.text = exam.enddate;
    _startTimeController.text = exam.starttime;
    _endTimeController.text = exam.endtime;
    // Set duration from exam data, but will be recalculated later
    _durationController.text =
        exam.duration > 0 ? exam.duration.toString() : '0';
    _kkmController.text = exam.kkm;
    _trialsController.text = exam.trials.toString();
    _selectedClassId = exam.classInfo?.id;
    _selectedQuestionMaterialId = exam.questionmaterial?.id;

    // Load questions for the selected material
    if (_selectedQuestionMaterialId != null) {
      // Use addPostFrameCallback to avoid calling during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        context.read<ExamProvider>().loadQuestionsByMaterial(
          _selectedQuestionMaterialId!,
          notify: false, // Don't notify during initial load
        );
      });
    }

    // Calculate duration based on current date/time values
    // Add a small delay to ensure all fields are populated
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _calculateDuration();
      }
    });
  }

  Future<void> _selectDate(TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        controller.text =
            "${picked.year}-${picked.month.toString().padLeft(2, '0')}-${picked.day.toString().padLeft(2, '0')}";
      });
      _calculateDuration();
    }
  }

  Future<void> _selectTime(TextEditingController controller) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );
    if (picked != null) {
      setState(() {
        controller.text =
            "${picked.hour.toString().padLeft(2, '0')}:${picked.minute.toString().padLeft(2, '0')}";
      });
      _calculateDuration();
    }
  }

  void _onQuestionMaterialChanged(int? materialId) {
    setState(() {
      _selectedQuestionMaterialId = materialId;
      _selectedQuestionIds.clear();
    });

    if (materialId != null) {
      context.read<ExamProvider>().loadQuestionsByMaterial(materialId);
    }
  }

  void _calculateDuration() {
    final startDate = _startDateController.text;
    final endDate = _endDateController.text;
    final startTime = _startTimeController.text;
    final endTime = _endTimeController.text;

    if (startDate.isNotEmpty &&
        endDate.isNotEmpty &&
        startTime.isNotEmpty &&
        endTime.isNotEmpty) {
      try {
        // Handle time format - ensure it's in HH:MM format for parsing
        String formattedStartTime = startTime;
        String formattedEndTime = endTime;

        // If time already includes seconds (HH:MM:SS), remove them
        if (startTime.split(':').length == 3) {
          formattedStartTime = startTime.substring(0, 5); // Take only HH:MM
        }
        if (endTime.split(':').length == 3) {
          formattedEndTime = endTime.substring(0, 5); // Take only HH:MM
        }

        final startDateTime = DateTime.parse(
          '$startDate $formattedStartTime:00',
        );
        final endDateTime = DateTime.parse('$endDate $formattedEndTime:00');

        final difference = endDateTime.difference(startDateTime);
        final durationInMinutes = difference.inMinutes;

        if (durationInMinutes > 0) {
          setState(() {
            _durationController.text = durationInMinutes.toString();
          });
        } else {
          setState(() {
            _durationController.text = '0';
          });
          if (durationInMinutes < 0) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Waktu selesai tidak boleh lebih awal dari waktu mulai!',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        // Invalid date/time format
        setState(() {
          _durationController.text = '0';
        });
      }
    } else {
      setState(() {
        _durationController.text = '0';
      });
    }
  }

  void _showQuestionSelection() {
    if (_selectedQuestionMaterialId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pilih mata pelajaran terlebih dahulu'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final questions = context.read<ExamProvider>().getQuestionsForMaterial(
      _selectedQuestionMaterialId!,
    );

    if (questions.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tidak ada soal untuk mata pelajaran ini'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.grey[300],
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      const Text(
                        'Pilih Soal',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        '${_selectedQuestionIds.length} dipilih',
                        style: const TextStyle(
                          color: Color(0xFF455A9D),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: ListView.builder(
                      itemCount: questions.length,
                      itemBuilder: (context, index) {
                        final question = questions[index];
                        final isSelected = _selectedQuestionIds.contains(
                          question.id,
                        );

                        return Container(
                          margin: const EdgeInsets.only(bottom: 8),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color:
                                  isSelected
                                      ? const Color(0xFF455A9D)
                                      : Colors.grey[300]!,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: CheckboxListTile(
                            value: isSelected,
                            onChanged: (bool? value) {
                              setModalState(() {
                                if (value == true) {
                                  _selectedQuestionIds.add(question.id);
                                } else {
                                  _selectedQuestionIds.remove(question.id);
                                }
                              });
                              setState(() {});
                            },
                            title: Text(
                              question.question,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            subtitle: Text(
                              'Tipe: ${question.type}',
                              style: const TextStyle(fontSize: 12),
                            ),
                            activeColor: const Color(0xFF455A9D),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF455A9D),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Selesai'),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _saveExam() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedClassId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pilih kelas terlebih dahulu'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_selectedQuestionMaterialId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pilih mata pelajaran terlebih dahulu'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_selectedQuestionIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Pilih minimal 1 soal'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final examProvider = context.read<ExamProvider>();
      bool success;

      if (isEditing) {
        success = await examProvider.updateExam(
          id: widget.exam!.id,
          name: _nameController.text,
          startdate: _startDateController.text,
          enddate: _endDateController.text,
          starttime: _startTimeController.text,
          endtime: _endTimeController.text,
          duration: int.parse(_durationController.text),
          kkm: _kkmController.text,
          classid: _selectedClassId!,
          questionmaterialid: _selectedQuestionMaterialId!,
          trials: int.parse(_trialsController.text),
          questionIds: _selectedQuestionIds,
        );
      } else {
        success = await examProvider.createExam(
          name: _nameController.text,
          startdate: _startDateController.text,
          enddate: _endDateController.text,
          starttime: _startTimeController.text,
          endtime: _endTimeController.text,
          duration: int.parse(_durationController.text),
          kkm: _kkmController.text,
          classid: _selectedClassId!,
          questionmaterialid: _selectedQuestionMaterialId!,
          trials: int.parse(_trialsController.text),
          questionIds: _selectedQuestionIds,
        );
      }

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isEditing
                  ? 'Jadwal ujian berhasil diperbarui'
                  : 'Jadwal ujian berhasil dibuat',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(examProvider.errorMessage ?? 'Terjadi kesalahan'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        backgroundColor: const Color(0xFF455A9D),
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          isEditing ? 'Edit Jadwal Ujian' : 'Tambah Jadwal Ujian',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: Consumer<ExamProvider>(
        builder: (context, examProvider, child) {
          if (examProvider.isLoading && examProvider.formData == null) {
            return const Center(
              child: CircularProgressIndicator(color: Color(0xFF455A9D)),
            );
          }

          return Form(
            key: _formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // Nama Ujian
                _buildTextField(
                  controller: _nameController,
                  label: 'Nama Ujian',
                  hint: 'Masukkan nama ujian',
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Nama ujian tidak boleh kosong';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Tanggal Mulai dan Selesai
                Row(
                  children: [
                    Expanded(
                      child: _buildDateField(
                        controller: _startDateController,
                        label: 'Tanggal Mulai',
                        hint: 'Pilih tanggal',
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDateField(
                        controller: _endDateController,
                        label: 'Tanggal Selesai',
                        hint: 'Pilih tanggal',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Waktu Mulai dan Selesai
                Row(
                  children: [
                    Expanded(
                      child: _buildTimeField(
                        controller: _startTimeController,
                        label: 'Waktu Mulai',
                        hint: 'Pilih waktu',
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTimeField(
                        controller: _endTimeController,
                        label: 'Waktu Selesai',
                        hint: 'Pilih waktu',
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Durasi dan KKM
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _durationController,
                        label: 'Durasi (menit)',
                        hint: 'Otomatis dihitung',
                        keyboardType: TextInputType.number,
                        readOnly: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Durasi tidak boleh kosong';
                          }
                          if (int.tryParse(value) == null ||
                              int.parse(value) <= 0) {
                            return 'Durasi harus berupa angka positif';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _kkmController,
                        label: 'KKM',
                        hint: 'Contoh: 75',
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'KKM tidak boleh kosong';
                          }
                          final kkm = double.tryParse(value);
                          if (kkm == null || kkm < 0 || kkm > 100) {
                            return 'KKM harus antara 0-100';
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Percobaan
                _buildTextField(
                  controller: _trialsController,
                  label: 'Jumlah Percobaan',
                  hint: 'Contoh: 3',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Jumlah percobaan tidak boleh kosong';
                    }
                    if (int.tryParse(value) == null || int.parse(value) <= 0) {
                      return 'Jumlah percobaan harus berupa angka positif';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Kelas Dropdown
                _buildDropdown<int>(
                  value: _selectedClassId,
                  label: 'Kelas',
                  hint: 'Pilih kelas',
                  items:
                      examProvider.formData?.classes.map((classInfo) {
                        return DropdownMenuItem<int>(
                          value: classInfo.id,
                          child: Text(classInfo.name),
                        );
                      }).toList() ??
                      [],
                  onChanged: (value) {
                    setState(() {
                      _selectedClassId = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Mata Pelajaran Dropdown
                _buildDropdown<int>(
                  value: _selectedQuestionMaterialId,
                  label: 'Mata Pelajaran',
                  hint: 'Pilih mata pelajaran',
                  items:
                      examProvider.formData?.questionMaterials.map((material) {
                        return DropdownMenuItem<int>(
                          value: material.id,
                          child: Text(material.name),
                        );
                      }).toList() ??
                      [],
                  onChanged: _onQuestionMaterialChanged,
                ),
                const SizedBox(height: 16),

                // Pilih Soal Button
                Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ListTile(
                    title: Text(
                      _selectedQuestionIds.isEmpty
                          ? 'Pilih Soal'
                          : '${_selectedQuestionIds.length} soal dipilih',
                      style: TextStyle(
                        color:
                            _selectedQuestionIds.isEmpty
                                ? Colors.grey[600]
                                : const Color(0xFF455A9D),
                        fontWeight:
                            _selectedQuestionIds.isEmpty
                                ? FontWeight.normal
                                : FontWeight.w500,
                      ),
                    ),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: _showQuestionSelection,
                  ),
                ),
                const SizedBox(height: 32),

                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveExam,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF455A9D),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child:
                        _isLoading
                            ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            )
                            : Text(
                              isEditing ? 'Perbarui Jadwal' : 'Simpan Jadwal',
                            ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool readOnly = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          validator: validator,
          readOnly: readOnly,
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF455A9D)),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildDateField({
    required TextEditingController controller,
    required String label,
    required String hint,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          readOnly: true,
          onTap: () => _selectDate(controller),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Tanggal tidak boleh kosong';
            }
            return null;
          },
          decoration: InputDecoration(
            hintText: hint,
            suffixIcon: const Icon(Icons.calendar_today, size: 20),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF455A9D)),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeField({
    required TextEditingController controller,
    required String label,
    required String hint,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          readOnly: true,
          onTap: () => _selectTime(controller),
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Waktu tidak boleh kosong';
            }
            return null;
          },
          decoration: InputDecoration(
            hintText: hint,
            suffixIcon: const Icon(Icons.access_time, size: 20),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF455A9D)),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildDropdown<T>({
    required T? value,
    required String label,
    required String hint,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          value: value,
          items: items,
          onChanged: onChanged,
          validator: (value) {
            if (value == null) {
              return '$label tidak boleh kosong';
            }
            return null;
          },
          decoration: InputDecoration(
            hintText: hint,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF455A9D)),
            ),
            filled: true,
            fillColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
