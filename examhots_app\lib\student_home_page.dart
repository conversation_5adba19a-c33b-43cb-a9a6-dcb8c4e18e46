import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'providers/auth_provider.dart';
import 'models/exam.dart';
import 'models/exam_result.dart';
import 'services/api_service.dart';
import 'pages/student/exam_page.dart';
import 'main.dart';

class StudentHomePage extends StatefulWidget {
  const StudentHomePage({super.key});

  @override
  State<StudentHomePage> createState() => _StudentHomePageState();
}

class _StudentHomePageState extends State<StudentHomePage> {
  List<Exam> _exams = [];
  bool _isLoading = true;
  String? _errorMessage;
  Map<int, ExamResult> _examResults = {}; // Store exam results by exam ID
  Map<int, Map<String, dynamic>> _examSettings =
      {}; // Store KKM and max attempts
  Map<int, Map<String, dynamic>> _examTimeStatus = {}; // Store exam time status

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkExamInProgress();
    });
  }

  // Helper method to safely parse dynamic values to int
  int? _parseToInt(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) return int.tryParse(value);
    return null;
  }

  // Check if there's an exam in progress and redirect to exam page
  Future<void> _checkExamInProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final examInProgressId = prefs.getInt('exam_in_progress');

    if (examInProgressId != null) {
      // Find the exam and redirect to exam page
      try {
        final response = await ApiService.getStudentExams();
        if (response['success'] == true) {
          final examList = response['data']['exams'] as List;
          final exams = examList.map((exam) => Exam.fromJson(exam)).toList();

          final examInProgress = exams.firstWhere(
            (exam) => exam.id == examInProgressId,
            orElse: () => throw Exception('Exam not found'),
          );

          // Check if exam is still active
          final now = DateTime.now();
          final examEnd = DateTime.parse(
            '${examInProgress.enddate} ${examInProgress.endtime}',
          );

          if (now.isBefore(examEnd)) {
            // Exam is still active, redirect to exam page
            if (mounted) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => ExamPage(exam: examInProgress),
                ),
              );
              return;
            }
          } else {
            // Exam has ended, clear the flag
            await prefs.remove('exam_in_progress');
            await prefs.remove('exam_start_time');
          }
        }
      } catch (e) {
        // If there's an error, clear the flag and continue
        await prefs.remove('exam_in_progress');
        await prefs.remove('exam_start_time');
      }
    }

    // Load exams normally
    _loadExams();
  }

  Future<void> _loadExams() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final response = await ApiService.getStudentExams();

      if (response['success'] == true) {
        final examList = response['data']['exams'] as List;
        final exams = examList.map((exam) => Exam.fromJson(exam)).toList();

        // Check exam results for each exam
        Map<int, ExamResult> results = {};
        Map<int, Map<String, dynamic>> examSettings =
            {}; // Store KKM and max attempts
        for (final exam in exams) {
          try {
            final resultResponse = await ApiService.getExamResult(exam.id);
            if (resultResponse['success'] == true) {
              // Create ExamResult with essay questions info
              final resultData = resultResponse['data']['result'];
              resultData['has_essay_questions'] =
                  resultResponse['data']['has_essay_questions'] ?? false;

              results[exam.id] = ExamResult.fromJson(resultData);

              // Store exam settings (ensure integers)
              examSettings[exam.id] = {
                'kkm': _parseToInt(resultResponse['data']['kkm']) ?? 75,
                'max_attempts':
                    _parseToInt(resultResponse['data']['max_attempts']) ?? 1,
              };
            }
          } catch (e) {
            // Exam result not found (student hasn't taken the exam yet)
            // This is normal, so we don't treat it as an error
          }
        }

        // Check exam time status for each exam
        Map<int, Map<String, dynamic>> timeStatus = {};
        for (final exam in exams) {
          try {
            final timeResponse = await ApiService.checkExamTime(exam.id);
            if (timeResponse['success'] == true) {
              timeStatus[exam.id] = timeResponse['data'];
            }
          } catch (e) {
            // Default to unknown status if check fails
            timeStatus[exam.id] = {
              'status': 'unknown',
              'can_start': false,
              'message': 'Tidak dapat mengecek status waktu ujian',
            };
          }
        }

        setState(() {
          _exams = exams;
          _examResults = results;
          _examSettings = examSettings;
          _examTimeStatus = timeStatus;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = response['message'] ?? 'Failed to load exams';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Logout'),
          content: const Text('Apakah Anda yakin ingin logout?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Batal'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _handleLogout();
              },
              child: const Text('Logout'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleLogout() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.logout();

    if (!mounted) return;

    // Navigate to login page
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => const LoginPage()),
      (route) => false,
    );
  }

  void _showTokenDialog(Exam exam) {
    final TextEditingController tokenController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Masukkan Token Ujian'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Masukkan token untuk ujian "${exam.name}"',
                style: const TextStyle(fontSize: 14),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: tokenController,
                decoration: const InputDecoration(
                  labelText: 'Token Ujian',
                  border: OutlineInputBorder(),
                  hintText: 'Masukkan token...',
                ),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 2,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('Batal'),
            ),
            ElevatedButton(
              onPressed: () {
                _validateTokenAndStartExam(exam, tokenController.text.trim());
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF455A9D),
                foregroundColor: Colors.white,
              ),
              child: const Text('Mulai Ujian'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _validateTokenAndStartExam(Exam exam, String inputToken) async {
    if (inputToken.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Token tidak boleh kosong'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => const Center(
              child: CircularProgressIndicator(color: Color(0xFF455A9D)),
            ),
      );

      final response = await ApiService.validateExamToken(inputToken);

      if (!mounted) return;

      // Close loading dialog
      Navigator.of(context).pop();

      if (response['success'] == true) {
        // Close token dialog
        Navigator.of(context).pop();
        _startExam(exam);
      } else {
        throw Exception(response['message'] ?? 'Token validation failed');
      }
    } catch (e) {
      if (!mounted) return;

      // Close loading dialog if still open
      if (Navigator.of(context).canPop()) {
        Navigator.of(context).pop();
      }

      String errorMessage = e.toString().replaceAll('Exception: ', '');

      // Check if this is an "already completed" error
      if (errorMessage.contains('Anda sudah menyelesaikan ujian ini')) {
        // Reload exams to update the UI with results
        _loadExams();
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _startExam(Exam exam) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ExamPage(exam: exam)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FD),
      body: SafeArea(
        child: Column(
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/login-bg.jpg'),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(24),
                    bottomRight: Radius.circular(24),
                  ),
                ),
                child: Column(
                  children: [
                    // Top Row with Student Info and Logout Button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Student Info Section
                        Consumer<AuthProvider>(
                          builder: (context, authProvider, child) {
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Selamat datang,',
                                  style: TextStyle(
                                    color: const Color(
                                      0xFF666666,
                                    ).withValues(alpha: 0.8),
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      authProvider.user?.name ?? 'Siswa',
                                      style: const TextStyle(
                                        color: Color(0xFF333333),
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: BoxDecoration(
                                        color: const Color(0xFF455A9D),
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: const Text(
                                        'SISWA',
                                        style: TextStyle(
                                          fontSize: 10,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (authProvider.user?.student?.studentClass !=
                                    null) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    'Kelas: ${authProvider.user!.student!.studentClass!.name}',
                                    style: const TextStyle(
                                      color: Color(0xFF666666),
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ],
                            );
                          },
                        ),
                        // Logout and Refresh Buttons
                        Row(
                          children: [
                            GestureDetector(
                              onTap: _loadExams,
                              child: Container(
                                width: 44,
                                height: 44,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: const BoxDecoration(
                                  color: Color(0xFF455A9D),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.refresh,
                                  color: Colors.white,
                                  size: 24,
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: _showLogoutDialog,
                              child: Container(
                                width: 44,
                                height: 44,
                                decoration: const BoxDecoration(
                                  color: Color(0xFF455A9D),
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.logout,
                                  color: Colors.white,
                                  size: 24,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    // Title Section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Text(
                        'Jadwal Ujian Tersedia',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF31406F),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: _buildExamList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExamList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: Color(0xFF455A9D)),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: const TextStyle(fontSize: 16, color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadExams,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF455A9D),
                foregroundColor: Colors.white,
              ),
              child: const Text('Coba Lagi'),
            ),
          ],
        ),
      );
    }

    if (_exams.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.assignment_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Belum ada jadwal ujian',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Jadwal ujian akan muncul di sini',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadExams,
      color: const Color(0xFF455A9D),
      child: ListView.builder(
        itemCount: _exams.length,
        itemBuilder: (context, index) {
          final exam = _exams[index];
          return _buildExamCard(exam);
        },
      ),
    );
  }

  Widget _buildExamCard(Exam exam) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Exam name
            Text(
              exam.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF31406F),
              ),
            ),
            const SizedBox(height: 8),

            // Subject
            if (exam.questionmaterial != null) ...[
              Row(
                children: [
                  const Icon(Icons.book, size: 16, color: Color(0xFF667085)),
                  const SizedBox(width: 8),
                  Text(
                    exam.questionmaterial!.name,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF667085),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // Date and time
            Row(
              children: [
                const Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Color(0xFF667085),
                ),
                const SizedBox(width: 8),
                Text(
                  exam.dateRange,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF667085),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 16,
                  color: Color(0xFF667085),
                ),
                const SizedBox(width: 8),
                Text(
                  exam.timeRange,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF667085),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Duration and questions
            Row(
              children: [
                Expanded(
                  child: Row(
                    children: [
                      const Icon(
                        Icons.timer,
                        size: 16,
                        color: Color(0xFF667085),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '${exam.duration} menit',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF667085),
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    const Icon(Icons.quiz, size: 16, color: Color(0xFF667085)),
                    const SizedBox(width: 8),
                    Text(
                      '${exam.amountquestion} soal',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF667085),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Check if exam is completed and if student can retake
            if (_examResults.containsKey(exam.id)) ...[
              // Check if student can retake based on score and attempts
              _buildExamResultOrRetakeButton(exam),
            ] else ...[
              // Show start exam button or time status
              _buildStartExamButton(exam),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStartExamButton(Exam exam) {
    final timeStatus = _examTimeStatus[exam.id];
    final canStart = timeStatus?['can_start'] ?? false;
    final status = timeStatus?['status'] ?? 'unknown';
    final message = timeStatus?['message'] ?? '';

    if (!canStart) {
      // Show disabled button with time status message
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: _getStatusColor(status).withValues(alpha: 0.1),
          border: Border.all(color: _getStatusColor(status)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Icon(
              _getStatusIcon(status),
              color: _getStatusColor(status),
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              _getStatusTitle(status),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: _getStatusColor(status),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              message,
              style: TextStyle(fontSize: 14, color: _getStatusColor(status)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    // Show normal start button if exam time is active
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _showTokenDialog(exam),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF455A9D),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
        child: const Text(
          'Mulai Ujian',
          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // Helper methods for exam time status
  Color _getStatusColor(String status) {
    switch (status) {
      case 'not_started':
        return Colors.orange;
      case 'ended':
        return Colors.red;
      case 'active':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'not_started':
        return Icons.schedule;
      case 'ended':
        return Icons.access_time_filled;
      case 'active':
        return Icons.play_circle;
      default:
        return Icons.help_outline;
    }
  }

  String _getStatusTitle(String status) {
    switch (status) {
      case 'not_started':
        return 'Ujian Belum Dimulai';
      case 'ended':
        return 'Waktu Ujian Berakhir';
      case 'active':
        return 'Ujian Aktif';
      default:
        return 'Status Tidak Diketahui';
    }
  }

  Widget _buildExamResultOrRetakeButton(Exam exam) {
    final result = _examResults[exam.id]!;
    final settings = _examSettings[exam.id];
    // Ensure all values are integers for comparison
    final kkm =
        _parseToInt(settings?['kkm']) ??
        int.tryParse(exam.kkm) ??
        75; // Get KKM from API or exam data
    final maxAttempts =
        _parseToInt(settings?['max_attempts']) ??
        exam.trials; // Get max attempts from API or exam data

    // Check if exam is still under manual grading (has essay questions and status is not 'graded')
    final isUnderManualGrading =
        result.hasEssayQuestions && result.status != 'graded';

    final canRetake =
        result.score < kkm &&
        result.totalAttempts < maxAttempts &&
        !isUnderManualGrading; // Prevent retry during manual grading

    if (canRetake) {
      // Show retake button with current score info
      return Column(
        children: [
          // Current score info
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              border: Border.all(color: Colors.orange[200]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: Colors.orange[600],
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      'Belum Mencapai KKM',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 6),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Skor Terbaik: ${result.formattedScore}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Color(0xFF667085),
                          ),
                        ),
                        if (result.hasEssayQuestions) ...[
                          const SizedBox(height: 2),
                          Text(
                            '(Sementara)',
                            style: TextStyle(
                              fontSize: 10,
                              fontStyle: FontStyle.italic,
                              color: Colors.orange[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      'Percobaan: ${result.totalAttempts}/$maxAttempts',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF667085),
                      ),
                    ),
                  ],
                ),
                // Add essay warning message for retake section
                if (result.hasEssayQuestions) ...[
                  const SizedBox(height: 6),
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.orange[50],
                      border: Border.all(color: Colors.orange[200]!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 14,
                          color: Colors.orange[600],
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            'Skor sementara - soal esai akan diperiksa manual oleh guru.',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.orange[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Retake button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _showTokenDialog(exam),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Ulangi Ujian',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              ),
            ),
          ),
        ],
      );
    } else if (isUnderManualGrading) {
      // Show manual grading in progress message
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange[50],
          border: Border.all(color: Colors.orange[200]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.hourglass_empty,
                  color: Colors.orange[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Ujian Dalam Pemeriksaan Manual',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Skor Sementara:',
                  style: TextStyle(fontSize: 14, color: Color(0xFF667085)),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      result.formattedScore,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF374151),
                      ),
                    ),
                    if (result.hasEssayQuestions)
                      const Text(
                        '(Menunggu koreksi esai)',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF667085),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            const Text(
              'Guru sedang memeriksa jawaban esai Anda. Ulangi ujian akan tersedia setelah pemeriksaan selesai.',
              style: TextStyle(fontSize: 12, color: Color(0xFF667085)),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    } else {
      // Show final result (passed KKM or max attempts reached)
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: result.score >= kkm ? Colors.green[50] : Colors.red[50],
          border: Border.all(
            color: result.score >= kkm ? Colors.green[200]! : Colors.red[200]!,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  result.score >= kkm ? Icons.check_circle : Icons.cancel,
                  color:
                      result.score >= kkm ? Colors.green[600] : Colors.red[600],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  result.score >= kkm
                      ? 'Ujian Selesai - Lulus'
                      : 'Ujian Selesai - Tidak Lulus',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: result.score >= kkm ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Skor Terbaik:',
                  style: TextStyle(fontSize: 14, color: Color(0xFF667085)),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      result.formattedScore,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF31406F),
                      ),
                    ),
                    if (result.hasEssayQuestions) ...[
                      const SizedBox(height: 2),
                      Text(
                        '(Sementara)',
                        style: TextStyle(
                          fontSize: 10,
                          fontStyle: FontStyle.italic,
                          color: Colors.orange[600],
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
            // Add essay warning message if exam contains essay questions
            if (result.hasEssayQuestions) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange[50],
                  border: Border.all(color: Colors.orange[200]!),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      size: 16,
                      color: Colors.orange[600],
                    ),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        'Skor ini bersifat sementara karena ujian mengandung soal esai yang akan diperiksa secara manual oleh guru.',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.orange[700],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Total Percobaan:',
                  style: TextStyle(fontSize: 12, color: Color(0xFF667085)),
                ),
                Text(
                  '${result.totalAttempts}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF667085),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Diselesaikan:',
                  style: TextStyle(fontSize: 12, color: Color(0xFF667085)),
                ),
                Text(
                  result.formattedSubmittedAt,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF667085),
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }
  }
}
