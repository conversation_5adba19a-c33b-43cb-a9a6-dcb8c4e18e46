<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Validation\Rule;

class TeachersController extends Controller
{
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $teachers = User::where('role', 'teacher')->get();

        return view('admin.management.teachers.index', [
            'title' => 'Guru',
            'teachers' => $teachers
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        return view('admin.management.teachers.add', [
            'title' => 'Tambah Guru'
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $request->validate([
                'nip' => 'required|numeric|unique:users,nip',
                'name' => 'required',
                'email' => 'required|email|unique:users,email',
                'password' => 'required',
                'gender' => 'required|in:male,female',
                'phonenumber' => 'required|numeric',
            ], [
                'nip.required' => 'NIP wajib diisi',
                'nip.numeric' => 'NIP harus berupa angka',
                'nip.unique' => 'NIP sudah digunakan, silakan gunakan NIP lain',
                'name.required' => 'Nama wajib diisi',
                'email.required' => 'Email wajib diisi',
                'email.email' => 'Format email tidak valid',
                'email.unique' => 'Email sudah digunakan, silakan gunakan email lain',
                'password.required' => 'Password wajib diisi',
                'gender.required' => 'Jenis kelamin wajib dipilih',
                'gender.in' => 'Jenis kelamin harus male atau female',
                'phonenumber.required' => 'Nomor telepon wajib diisi',
                'phonenumber.numeric' => 'Nomor telepon harus berupa angka',
            ]);

            $phonenumber = $request->phonenumber;
            if (substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            User::create([
                'nip' => $request->nip,
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'gender' => $request->gender,
                'role' => 'teacher',
                'phonenumber' => $phonenumber,
                'address' => $request->address ?? null,
            ]);

            return redirect()->route('teachers.index')->with('success', 'Data guru berhasil ditambahkan');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('teachers.index')->with('error', 'Gagal menambahkan data guru: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $teachers = User::find($id);

        return view('admin.management.teachers.edit', [
            'title' => 'Edit Guru',
            'teachers' => $teachers
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $teachers = User::find($id);
            if (!$teachers) {
                return redirect()->route('teachers.index')->with('error', 'Guru tidak ditemukan');
            }

            $request->validate([
                'nip' => [
                    'required',
                    'numeric',
                    Rule::unique('users', 'nip')->ignore($id),
                ],
                'name' => 'required',
                'email' => [
                    'required',
                    'email',
                    Rule::unique('users')->ignore($id),
                ],
                'gender' => 'required|in:male,female',
                'phonenumber' => 'required|numeric',
            ], [
                'nip.required' => 'NIP wajib diisi',
                'nip.numeric' => 'NIP harus berupa angka',
                'nip.unique' => 'NIP sudah digunakan, silakan gunakan NIP lain',
                'name.required' => 'Nama wajib diisi',
                'email.required' => 'Email wajib diisi',
                'email.email' => 'Format email tidak valid',
                'email.unique' => 'Email sudah digunakan, silakan gunakan email lain',
                'gender.required' => 'Jenis kelamin wajib dipilih',
                'gender.in' => 'Jenis kelamin harus male atau female',
                'phonenumber.required' => 'Nomor telepon wajib diisi',
                'phonenumber.numeric' => 'Nomor telepon harus berupa angka',
            ]);

            $phonenumber = $request->phonenumber;
            if (substr($phonenumber, 0, 2) === '08') {
                $phonenumber = '62' . substr($phonenumber, 2);
            }

            $teachers->nip = $request->nip;
            $teachers->name = $request->name;
            $teachers->email = $request->email;

            if ($request->password) {
                $teachers->password = bcrypt($request->password);
            }

            $teachers->gender = $request->gender;
            $teachers->phonenumber = $phonenumber;
            $teachers->address = $request->address ?? null;
            $teachers->role = 'teacher';

            $teachers->save();

            return redirect()->route('teachers.index')->with('success', 'Data guru berhasil diubah');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
        } catch (\Exception $e) {
            return redirect()->route('teachers.index')->with('error', 'Gagal mengubah data guru: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $teachers = User::find($id);

        $teachers->delete();

        return redirect()->route('teachers.index')->with('success', 'Data guru berhasil dihapus');
    }
}
