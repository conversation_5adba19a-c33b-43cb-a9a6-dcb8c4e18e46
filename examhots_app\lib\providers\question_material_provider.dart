import 'package:flutter/foundation.dart';
import '../models/question_material.dart';
import '../services/api_service.dart';

class QuestionMaterialProvider with ChangeNotifier {
  List<QuestionMaterial> _questionMaterials = [];
  bool _isLoading = false;
  String? _errorMessage;

  List<QuestionMaterial> get questionMaterials => _questionMaterials;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error message
  void _setError(String error) {
    _errorMessage = error;
    _setLoading(false);
  }

  // Load all question materials
  Future<void> loadQuestionMaterials() async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.getQuestionMaterials();

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        _questionMaterials =
            data.map((json) => QuestionMaterial.fromJson(json)).toList();
      } else {
        _setError(response['message'] ?? 'Failed to load question materials');
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
    } finally {
      _setLoading(false);
    }
  }

  // Get specific question material
  Future<QuestionMaterial?> getQuestionMaterial(int id) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.getQuestionMaterial(id);

      if (response['success'] == true) {
        return QuestionMaterial.fromJson(response['data']);
      } else {
        _setError(response['message'] ?? 'Failed to get question material');
        return null;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Create new question material
  Future<bool> createQuestionMaterial(
    String name,
    String description, {
    int? teacherId,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.createQuestionMaterial(
        name,
        description,
        teacherId: teacherId,
      );

      if (response['success'] == true) {
        // Add the new question material to the list
        final newMaterial = QuestionMaterial.fromJson(response['data']);
        _questionMaterials.add(newMaterial);
        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to create question material');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update question material
  Future<bool> updateQuestionMaterial(
    int id,
    String name,
    String description, {
    int? teacherId,
  }) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.updateQuestionMaterial(
        id,
        name,
        description,
        teacherId: teacherId,
      );

      if (response['success'] == true) {
        // Update the question material in the list
        final updatedMaterial = QuestionMaterial.fromJson(response['data']);
        final index = _questionMaterials.indexWhere(
          (material) => material.id == id,
        );
        if (index != -1) {
          _questionMaterials[index] = updatedMaterial;
          notifyListeners();
        }
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to update question material');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Delete question material
  Future<bool> deleteQuestionMaterial(int id) async {
    try {
      _setLoading(true);
      clearError();

      final response = await ApiService.deleteQuestionMaterial(id);

      if (response['success'] == true) {
        // Remove the question material from the list
        _questionMaterials.removeWhere((material) => material.id == id);
        notifyListeners();
        return true;
      } else {
        _setError(response['message'] ?? 'Failed to delete question material');
        return false;
      }
    } catch (e) {
      _setError(e.toString().replaceAll('Exception: ', ''));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Refresh question materials
  Future<void> refresh() async {
    await loadQuestionMaterials();
  }

  // Search question materials by name or description
  List<QuestionMaterial> searchQuestionMaterials(String query) {
    if (query.isEmpty) return _questionMaterials;

    return _questionMaterials.where((material) {
      return material.name.toLowerCase().contains(query.toLowerCase()) ||
          material.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }
}
