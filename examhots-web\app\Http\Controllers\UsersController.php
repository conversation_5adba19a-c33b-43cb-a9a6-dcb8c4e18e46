<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\User;
use Illuminate\Validation\Rule;

class UsersController extends Controller
{
    public function index()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $users = User::where('role', 'admin')->get();

        return view('admin.management.users.index', [
            'title' => 'Users',
            'users' => $users
        ]);
    }

    public function add()
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        return view('admin.management.users.add', [
            'title' => 'Add User'
        ]);
    }

    public function processAdd(Request $request)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $request->validate([
                'name' => 'required',
                'email' => 'required|email|unique:users,email',
                'password' => 'required',
            ], [
                'name.required' => 'Nama wajib diisi',
                'email.required' => 'Email wajib diisi',
                'email.email' => 'Format email tidak valid',
                'email.unique' => 'Email sudah digunakan, silakan gunakan email lain',
                'password.required' => 'Password wajib diisi',
            ]);

            User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'role' => 'admin', // Default to admin
            ]);

            return redirect()->route('users.index')->with('success', 'Data user berhasil ditambahkan');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            return redirect()->route('users.index')->with('error', 'Gagal menambahkan data user: ' . $e->getMessage());
        }
    }

    public function edit($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $users = User::find($id);

        return view('admin.management.users.edit', [
            'title' => 'Edit User',
            'users' => $users
        ]);
    }

    public function processEdit(Request $request, $id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        try {
            $users = User::find($id);

            if (!$users) {
                return redirect()->route('users.index')->with('error', 'User tidak ditemukan.');
            }

            $request->validate([
                'name' => 'required',
                'email' => [
                    'required',
                    'email',
                    Rule::unique('users')->ignore($id),
                ],
            ], [
                'name.required' => 'Nama wajib diisi',
                'email.required' => 'Email wajib diisi',
                'email.email' => 'Format email tidak valid',
                'email.unique' => 'Email sudah digunakan, silakan gunakan email lain',
            ]);

            $users->name = $request->name;
            $users->email = $request->email;
            if ($request->password) {
                $users->password = bcrypt($request->password);
            }
            $users->role = 'admin'; // Default to admin

            $users->save();

            return redirect()->route('users.index')->with('success', 'Data user berhasil diubah');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput()->with('error', 'Validasi gagal. Silakan periksa kembali data yang dimasukkan.');
        } catch (\Exception $e) {
            return redirect()->route('users.index')->with('error', 'Gagal mengubah data user: ' . $e->getMessage());
        }
    }

    public function delete($id)
    {
        if (!Auth::check()) {
            return redirect()->route('auth.login');
        }

        $users = User::find($id);

        $users->delete();

        return redirect()->route('users.index')->with('success', 'Data user berhasil dihapus');
    }
}
